'use client';

import { useState } from 'react';
// Icon components to replace lucide-react
const AlertCircleIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const RefreshCwIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
);

const SettingsIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const ZapIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
  </svg>
);

const ClockIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/utils/cn';
import { type ModelConfig } from '@/components/ModelSelector';

interface ErrorRecoveryProps {
  error: {
    type: 'network' | 'rate_limit' | 'timeout' | 'api_error' | 'unknown';
    message: string;
    isRetryable: boolean;
    canSwitchModel: boolean;
    retryCount?: number;
  };
  currentModel: ModelConfig;
  onRetry: () => void;
  onSwitchModel: () => void;
  onDismiss: () => void;
  className?: string;
}

// 错误类型配置
const ERROR_CONFIG = {
  network: {
    icon: AlertCircleIcon,
    title: '网络连接问题',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    description: '服务器暂时无法响应',
    suggestions: ['检查网络连接', '稍后重试', '尝试切换模型']
  },
  rate_limit: {
    icon: ClockIcon,
    title: '请求过于频繁',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    description: '为了保证服务质量，请稍等片刻',
    suggestions: ['等待一分钟后重试', '避免连续快速发送', '尝试切换模型']
  },
  timeout: {
    icon: ClockIcon,
    title: '请求超时',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    description: '服务器响应时间过长',
    suggestions: ['重新发送消息', '检查网络状况', '尝试切换模型']
  },
  api_error: {
    icon: AlertCircleIcon,
    title: 'API调用错误',
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    description: '请求格式或内容有误',
    suggestions: ['检查输入内容', '尝试重新组织问题', '切换到其他模型']
  },
  unknown: {
    icon: AlertCircleIcon,
    title: '未知错误',
    color: 'text-gray-600',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    description: '遇到了一些技术问题',
    suggestions: ['稍后重试', '刷新页面', '尝试切换模型']
  }
};

// 模型显示名称映射
const MODEL_DISPLAY_NAMES = {
  google: 'Google Gemini',
  'openai-htsc': 'HTSC DeepSeek',
  'openai-openrouter': 'OpenRouter Gemini',
  'openai-local': 'Local Model'
};

function getModelDisplayName(config: ModelConfig): string {
  if (config.type === 'google') {
    return MODEL_DISPLAY_NAMES.google;
  }
  return MODEL_DISPLAY_NAMES[`${config.type}-${config.providerType}` as keyof typeof MODEL_DISPLAY_NAMES] || 'Unknown Model';
}

export function ErrorRecovery({
  error,
  currentModel,
  onRetry,
  onSwitchModel,
  onDismiss,
  className
}: ErrorRecoveryProps) {
  const [isRetrying, setIsRetrying] = useState(false);
  const [isSwitching, setIsSwitching] = useState(false);

  const config = ERROR_CONFIG[error.type];
  const Icon = config.icon;

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await onRetry();
    } finally {
      setIsRetrying(false);
    }
  };

  const handleSwitchModel = async () => {
    setIsSwitching(true);
    try {
      await onSwitchModel();
    } finally {
      setIsSwitching(false);
    }
  };

  return (
    <Card className={cn(
      'w-full max-w-md mx-auto',
      config.bgColor,
      config.borderColor,
      'border-2',
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-3">
          <div className={cn(
            'p-2 rounded-full',
            config.bgColor,
            'ring-2 ring-white'
          )}>
            <Icon className={cn('h-5 w-5', config.color)} />
          </div>
          <div className="flex-1">
            <CardTitle className={cn('text-lg', config.color)}>
              {config.title}
            </CardTitle>
            <CardDescription className="text-sm">
              {config.description}
            </CardDescription>
          </div>
          {error.retryCount && error.retryCount > 0 && (
            <Badge variant="outline" className="text-xs">
              重试 {error.retryCount}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 错误详情 */}
        <div className="text-sm text-gray-600 bg-white/50 p-3 rounded-lg">
          <p className="font-medium mb-1">错误详情:</p>
          <p className="text-xs text-gray-500">{error.message}</p>
        </div>

        {/* 当前模型信息 */}
        <div className="flex items-center gap-2 text-sm">
          <SettingsIcon className="h-4 w-4 text-gray-400" />
          <span className="text-gray-600">当前模型:</span>
          <Badge variant="secondary" className="text-xs">
            {getModelDisplayName(currentModel)}
          </Badge>
        </div>

        {/* 建议操作 */}
        <div className="space-y-2">
          <p className="text-sm font-medium text-gray-700">建议操作:</p>
          <ul className="text-xs text-gray-600 space-y-1">
            {config.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-center gap-2">
                <div className="w-1 h-1 bg-gray-400 rounded-full" />
                {suggestion}
              </li>
            ))}
          </ul>
        </div>

        {/* 操作按钮 */}
        <div className="flex gap-2 pt-2">
          {error.isRetryable && (
            <Button
              onClick={handleRetry}
              disabled={isRetrying}
              size="sm"
              className="flex-1"
              variant="default"
            >
              {isRetrying ? (
                <>
                  <RefreshCwIcon className="h-4 w-4 mr-1 animate-spin" />
                  重试中...
                </>
              ) : (
                <>
                  <RefreshCwIcon className="h-4 w-4 mr-1" />
                  重试
                </>
              )}
            </Button>
          )}

          {error.canSwitchModel && (
            <Button
              onClick={handleSwitchModel}
              disabled={isSwitching}
              size="sm"
              className="flex-1"
              variant="outline"
            >
              {isSwitching ? (
                <>
                  <ZapIcon className="h-4 w-4 mr-1 animate-pulse" />
                  切换中...
                </>
              ) : (
                <>
                  <ZapIcon className="h-4 w-4 mr-1" />
                  切换模型
                </>
              )}
            </Button>
          )}

          <Button
            onClick={onDismiss}
            size="sm"
            variant="ghost"
            className="px-3"
          >
            ✕
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// 简化版错误恢复组件，用于内联显示
export function InlineErrorRecovery({
  error,
  onRetry,
  onSwitchModel,
  className
}: Omit<ErrorRecoveryProps, 'currentModel' | 'onDismiss'>) {
  const config = ERROR_CONFIG[error.type];
  const Icon = config.icon;

  return (
    <div className={cn(
      'flex items-center gap-3 p-3 rounded-lg border',
      config.bgColor,
      config.borderColor,
      className
    )}>
      <Icon className={cn('h-4 w-4', config.color)} />
      <div className="flex-1 min-w-0">
        <p className={cn('text-sm font-medium', config.color)}>
          {config.title}
        </p>
        <p className="text-xs text-gray-500 truncate">
          {error.message}
        </p>
      </div>
      <div className="flex gap-1">
        {error.isRetryable && (
          <Button size="sm" variant="outline" onClick={onRetry}>
            <RefreshCwIcon className="h-3 w-3" />
          </Button>
        )}
        {error.canSwitchModel && (
          <Button size="sm" variant="outline" onClick={onSwitchModel}>
            <ZapIcon className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}
