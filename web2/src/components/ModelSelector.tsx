'use client';

import { useState } from 'react';
// Icon components to replace lucide-react
const SettingsIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const ChevronDownIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/utils/cn';

export type ModelType = 'google' | 'openai';
export type OpenaiProviderType = 'htsc' | 'openrouter' | 'local';

export interface ModelConfig {
  type: ModelType;
  providerType?: OpenaiProviderType;
}

interface ModelSelectorProps {
  value: ModelConfig;
  onChange: (config: ModelConfig) => void;
  disabled?: boolean;
  className?: string;
}

const MODEL_OPTIONS = [
  {
    type: 'google' as ModelType,
    label: 'Google Gemini',
    description: 'Google Gemini 2.5 Flash',
  },
  {
    type: 'openai' as ModelType,
    providerType: 'htsc' as OpenaiProviderType,
    label: 'HTSC DeepSeek',
    description: 'ht::saas-deepseek-v3',
  },
  {
    type: 'openai' as ModelType,
    providerType: 'openrouter' as OpenaiProviderType,
    label: 'OpenRouter Gemini',
    description: 'google/gemini-2.5-flash',
  },
  {
    type: 'openai' as ModelType,
    providerType: 'local' as OpenaiProviderType,
    label: 'Local Model',
    description: 'gemini-2.5-flash (local)',
  },
];

function getModelLabel(config: ModelConfig): string {
  const option = MODEL_OPTIONS.find(
    opt => opt.type === config.type && 
    (config.type === 'google' || opt.providerType === config.providerType)
  );
  return option?.label || 'Unknown Model';
}

export function ModelSelector({ value, onChange, disabled, className }: ModelSelectorProps) {
  const [open, setOpen] = useState(false);

  const handleSelect = (option: typeof MODEL_OPTIONS[0]) => {
    const newConfig: ModelConfig = {
      type: option.type,
      ...(option.providerType && { providerType: option.providerType }),
    };
    onChange(newConfig);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          disabled={disabled}
          className={cn(
            "h-8 px-2 text-xs text-gray-600 hover:text-gray-900 hover:bg-gray-100",
            className
          )}
        >
          <SettingsIcon className="h-3 w-3 mr-1" />
          {getModelLabel(value)}
          <ChevronDownIcon className="h-3 w-3 ml-1" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-56 p-0">
        <div className="p-2">
          <div className="px-2 py-1.5 text-sm font-semibold">选择模型</div>
          <div className="-mx-1 my-1 h-px bg-gray-200"></div>
          {MODEL_OPTIONS.map((option, index) => (
            <button
              key={index}
              onClick={() => handleSelect(option)}
              className="w-full flex flex-col items-start py-2 px-2 text-left hover:bg-gray-100 rounded-sm"
            >
              <div className="font-medium text-sm">{option.label}</div>
              <div className="text-xs text-gray-500">{option.description}</div>
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}
