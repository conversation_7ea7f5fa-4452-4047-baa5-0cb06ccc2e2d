import * as React from 'react';
import { Checkbox as <PERSON><PERSON>Check<PERSON> } from '@heroui/react';
import { cn } from '@/utils/cn';

interface CheckboxProps {
  className?: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  [key: string]: any;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, checked, onCheckedChange, ...props }, ref) => (
    <HeroUICheckbox
      ref={ref}
      isSelected={checked}
      onValueChange={onCheckedChange}
      className={cn('', className)}
      size="sm"
      {...props}
    />
  )
);

Checkbox.displayName = 'Checkbox';

export { Checkbox };
