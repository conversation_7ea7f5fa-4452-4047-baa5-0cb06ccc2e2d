"use client"

import * as React from "react"
import { Select as HeroUISelect, SelectItem as HeroUISelectItem } from "@heroui/react"
import { cn } from "@/utils/cn"

// Wrapper components to maintain API compatibility
interface SelectProps {
  children: React.ReactNode;
  value?: string;
  onValueChange?: (value: string) => void;
  defaultValue?: string;
  disabled?: boolean;
}

const Select: React.FC<SelectProps> = ({ children, value, onValueChange, defaultValue, disabled }) => {
  // Extract items from children
  const items = React.Children.toArray(children).filter(
    (child): child is React.ReactElement<SelectItemProps> =>
      React.isValidElement(child) && child.type === SelectItem
  );

  return (
    <HeroUISelect
      selectedKeys={value ? [value] : undefined}
      onSelectionChange={(keys) => {
        const selectedKey = Array.from(keys)[0] as string;
        onValueChange?.(selectedKey);
      }}
      defaultSelectedKeys={defaultValue ? [defaultValue] : undefined}
      isDisabled={disabled}
      className="max-w-xs"
    >
      {items.map((item, index) => (
        <HeroUISelectItem key={item.props.value || index}>
          {item.props.children}
        </HeroUISelectItem>
      ))}
    </HeroUISelect>
  );
};

// Compatibility components
const SelectGroup: React.FC<{ children: React.ReactNode }> = ({ children }) => <>{children}</>;
const SelectValue: React.FC<{ placeholder?: string }> = () => null; // HeroUI handles this internally
const SelectTrigger: React.FC<{ children: React.ReactNode; className?: string }> = ({ children }) => <>{children}</>;
const SelectContent: React.FC<{ children: React.ReactNode }> = ({ children }) => <>{children}</>;
const SelectLabel: React.FC<{ children: React.ReactNode }> = ({ children }) => <>{children}</>;

interface SelectItemProps {
  value: string;
  children: React.ReactNode;
  disabled?: boolean;
  className?: string;
}

const SelectItem: React.FC<SelectItemProps> = ({ value, children, disabled, className }) => (
  <HeroUISelectItem key={value} isDisabled={disabled} className={className}>
    {children}
  </HeroUISelectItem>
);

const SelectSeparator: React.FC<{ className?: string }> = () => null; // HeroUI doesn't need separators
const SelectScrollUpButton: React.FC = () => null; // HeroUI handles scrolling internally
const SelectScrollDownButton: React.FC = () => null; // HeroUI handles scrolling internally

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
}
