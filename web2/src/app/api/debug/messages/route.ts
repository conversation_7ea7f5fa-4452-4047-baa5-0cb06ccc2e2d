import { NextRequest, NextResponse } from 'next/server';
import { getSessionMessages, getCurrentSessionState } from '@/utils/session-manager';
import { validateMessageArray } from '@/utils/message-validator';

/**
 * 消息调试API
 * GET /api/debug/messages?thread_id=xxx - 调试特定会话的消息
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const thread_id = searchParams.get('thread_id');
    const action = searchParams.get('action') || 'analyze';

    if (!thread_id) {
      return NextResponse.json(
        { error: '缺少 thread_id 参数' },
        { status: 400 }
      );
    }

    if (action === 'analyze') {
      // 获取原始状态数据
      const currentState = await getCurrentSessionState(thread_id);
      
      if (!currentState || !currentState.state) {
        return NextResponse.json({
          thread_id,
          error: '会话状态不存在',
          timestamp: new Date().toISOString()
        });
      }

      // 获取标准化后的消息
      const normalizedMessages = await getSessionMessages(thread_id, 100);
      
      // 验证消息
      const validation = validateMessageArray(normalizedMessages);

      // 分析原始消息数据
      const rawMessages = Array.isArray(currentState.state.messages) ? currentState.state.messages : [];
      const rawAnalysis = {
        total: rawMessages.length,
        typeDistribution: {} as Record<string, number>,
        roleDistribution: {} as Record<string, number>,
        missingFields: {
          noType: 0,
          noRole: 0,
          noContent: 0,
          noTimestamp: 0
        }
      };

      rawMessages.forEach(msg => {
        // 分析类型分布
        const type = msg._type || msg.type || 'undefined';
        rawAnalysis.typeDistribution[type] = (rawAnalysis.typeDistribution[type] || 0) + 1;

        // 分析角色分布
        const role = msg.role || 'undefined';
        rawAnalysis.roleDistribution[role] = (rawAnalysis.roleDistribution[role] || 0) + 1;

        // 分析缺失字段
        if (!msg._type && !msg.type) rawAnalysis.missingFields.noType++;
        if (!msg.role) rawAnalysis.missingFields.noRole++;
        if (!msg.content && msg.content !== '') rawAnalysis.missingFields.noContent++;
        if (!msg.timestamp) rawAnalysis.missingFields.noTimestamp++;
      });

      return NextResponse.json({
        thread_id,
        analysis: {
          raw: rawAnalysis,
          normalized: validation.stats,
          validation: {
            isValid: validation.isValid,
            issues: validation.issues
          }
        },
        samples: {
          raw: rawMessages.slice(0, 3).map(msg => ({
            _type: msg._type,
            type: msg.type,
            role: msg.role,
            content: msg.content?.substring(0, 100) + '...',
            hasTimestamp: !!msg.timestamp
          })),
          normalized: normalizedMessages.slice(0, 3)
        },
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'raw') {
      // 返回原始消息数据
      const currentState = await getCurrentSessionState(thread_id);
      
      if (!currentState || !currentState.state) {
        return NextResponse.json({
          thread_id,
          error: '会话状态不存在',
          timestamp: new Date().toISOString()
        });
      }

      const rawMessages = Array.isArray(currentState.state.messages) ? currentState.state.messages : [];
      
      return NextResponse.json({
        thread_id,
        raw_messages: rawMessages,
        total_count: rawMessages.length,
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'normalized') {
      // 返回标准化后的消息数据
      const normalizedMessages = await getSessionMessages(thread_id, 100);
      
      return NextResponse.json({
        thread_id,
        normalized_messages: normalizedMessages,
        total_count: normalizedMessages.length,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { error: `不支持的操作: ${action}` },
      { status: 400 }
    );

  } catch (error) {
    console.error('Messages Debug API: 错误', error);
    return NextResponse.json(
      { error: '调试消息失败' },
      { status: 500 }
    );
  }
}
