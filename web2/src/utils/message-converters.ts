import { Message } from 'ai';
import { AIMessage, BaseMessage, ChatMessage, HumanMessage } from '@langchain/core/messages';

export const convertVercelMessageToLangChainMessage = (message: Message) => {
  if (message.role === 'user') {
    return new HumanMessage(message.content);
  } else if (message.role === 'assistant') {
    return new AIMessage(message.content);
  } else {
    return new ChatMessage(message.content, message.role);
  }
};

export const convertLangChainMessageToVercelMessage = (message: BaseMessage) => {
  const messageType = message.getType();

  if (messageType === 'human') {
    return {
      content: message.content,
      role: 'user' as const,
      type: 'human'
    };
  } else if (messageType === 'ai') {
    return {
      content: message.content,
      role: 'assistant' as const,
      type: 'ai',
      parts: (message as AIMessage).tool_calls,
    };
  } else if (messageType === 'tool') {
    return {
      content: message.content,
      role: 'system' as const,
      type: 'tool'
    };
  } else if (messageType === 'system') {
    return {
      content: message.content,
      role: 'system' as const,
      type: 'system'
    };
  } else {
    // 对于未知类型，保持原始类型信息
    return {
      content: message.content,
      role: 'unknown' as const,
      type: messageType || 'unknown'
    };
  }
};
