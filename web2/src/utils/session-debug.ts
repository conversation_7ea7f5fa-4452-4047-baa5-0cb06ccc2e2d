import { get<PERSON>ongo<PERSON><PERSON>ckpointer, getMongoClient } from '@/config/mongodb';
import { getCurrentSessionState, getSessionMessages } from './session-manager';
import { SESSIONS, CHECKPOINT_COLLECTION_NAME, CHECKPOINT_WRITES_COLLECTION_NAME } from '../config/constants';

/**
 * 会话调试工具
 * 用于诊断和监控LangGraph会话状态
 */

export interface SessionDiagnostics {
  thread_id: string;
  session_exists: boolean;
  checkpoint_exists: boolean;
  message_count: number;
  last_checkpoint_time?: string;
  state_summary: {
    has_messages: boolean;
    has_input: boolean;
    has_output: boolean;
    state_keys: string[];
  };
  checkpointer_health: boolean;
  mongodb_collections: {
    sessions: number;
    checkpoints: number;
    checkpoint_writes: number;
  };
  recommendations: string[];
}

/**
 * 诊断会话状态
 */
export async function diagnoseSession(thread_id: string): Promise<SessionDiagnostics> {
  const diagnostics: SessionDiagnostics = {
    thread_id,
    session_exists: false,
    checkpoint_exists: false,
    message_count: 0,
    state_summary: {
      has_messages: false,
      has_input: false,
      has_output: false,
      state_keys: []
    },
    checkpointer_health: false,
    mongodb_collections: {
      sessions: 0,
      checkpoints: 0,
      checkpoint_writes: 0
    },
    recommendations: []
  };

  try {
    // 1. 检查MongoDB连接和集合状态
    const client = await getMongoClient();
    const db = client.db();
    
    diagnostics.checkpointer_health = true;
    
    // 统计各集合的文档数量
    const [sessionsCount, checkpointsCount, checkpointWritesCount] = await Promise.all([
      db.collection(SESSIONS).countDocuments({ thread_id }),
      db.collection(CHECKPOINT_COLLECTION_NAME).countDocuments({ thread_id }),
      db.collection(CHECKPOINT_WRITES_COLLECTION_NAME).countDocuments({ thread_id })
    ]);
    
    diagnostics.mongodb_collections = {
      sessions: sessionsCount,
      checkpoints: checkpointsCount,
      checkpoint_writes: checkpointWritesCount
    };

    // 2. 检查会话是否存在
    const sessionDoc = await db.collection(SESSIONS).findOne({ thread_id });
    diagnostics.session_exists = !!sessionDoc;

    // 3. 检查检查点状态
    const currentState = await getCurrentSessionState(thread_id);
    diagnostics.checkpoint_exists = !!currentState;
    
    if (currentState) {
      diagnostics.last_checkpoint_time = currentState.timestamp;
      
      // 分析状态内容
      const state = currentState.state || {};
      diagnostics.state_summary = {
        has_messages: Array.isArray(state.messages) && state.messages.length > 0,
        has_input: !!state.input,
        has_output: !!state.output,
        state_keys: Object.keys(state)
      };
    }

    // 4. 获取消息数量
    const messages = await getSessionMessages(thread_id);
    diagnostics.message_count = messages.length;

    // 5. 生成建议
    diagnostics.recommendations = generateRecommendations(diagnostics);

  } catch (error) {
    console.error('诊断会话失败:', error);
    diagnostics.recommendations.push(`诊断过程中出现错误: ${error instanceof Error ? error.message : '未知错误'}`);
  }

  return diagnostics;
}

/**
 * 生成优化建议
 */
function generateRecommendations(diagnostics: SessionDiagnostics): string[] {
  const recommendations: string[] = [];

  // 检查基本状态
  if (!diagnostics.checkpointer_health) {
    recommendations.push('MongoDB连接异常，请检查数据库配置');
  }

  if (!diagnostics.session_exists) {
    recommendations.push('会话记录不存在，可能需要重新创建会话');
  }

  if (!diagnostics.checkpoint_exists) {
    recommendations.push('检查点不存在，会话可能未正确初始化');
  }

  // 检查数据完整性
  if (diagnostics.session_exists && !diagnostics.checkpoint_exists) {
    recommendations.push('会话记录存在但检查点缺失，建议重新初始化会话');
  }

  if (diagnostics.checkpoint_exists && !diagnostics.state_summary.has_messages) {
    recommendations.push('检查点存在但缺少消息数据，可能存在状态同步问题');
  }

  // 检查性能问题
  if (diagnostics.message_count > 100) {
    recommendations.push('消息数量较多，建议实施消息清理策略以优化性能');
  }

  if (diagnostics.mongodb_collections.checkpoints > 50) {
    recommendations.push('检查点数量较多，建议定期清理旧检查点');
  }

  // 检查状态结构
  if (diagnostics.state_summary.state_keys.length === 0) {
    recommendations.push('状态为空，可能需要重新初始化图状态');
  }

  if (!diagnostics.state_summary.has_input && !diagnostics.state_summary.has_output) {
    recommendations.push('缺少输入输出数据，检查图的执行流程');
  }

  if (recommendations.length === 0) {
    recommendations.push('会话状态正常，无需特殊处理');
  }

  return recommendations;
}

/**
 * 修复常见的会话问题
 */
export async function repairSession(thread_id: string, options: {
  recreateSession?: boolean;
  clearCheckpoints?: boolean;
  resetState?: boolean;
} = {}): Promise<{ success: boolean; message: string; actions: string[] }> {
  const actions: string[] = [];
  
  try {
    const client = await getMongoClient();
    const db = client.db();
    
    if (options.clearCheckpoints) {
      // 清除检查点数据
      await Promise.all([
        db.collection(CHECKPOINT_COLLECTION_NAME).deleteMany({ thread_id }),
        db.collection(CHECKPOINT_WRITES_COLLECTION_NAME).deleteMany({ thread_id })
      ]);
      actions.push('已清除检查点数据');
    }

    if (options.recreateSession) {
      // 重新创建会话记录
      const sessionData = {
        thread_id,
        user_id: 'system',
        session_type: 'chat',
        title: '修复的会话',
        description: '通过调试工具修复的会话',
        created_at: new Date(),
        last_accessed: new Date(),
        updated_at: new Date(),
        status: 'active',
        message_count: 0,
        checkpoint_count: 0
      };
      
      await db.collection(SESSIONS).replaceOne(
        { thread_id },
        sessionData,
        { upsert: true }
      );
      actions.push('已重新创建会话记录');
    }

    return {
      success: true,
      message: '会话修复完成',
      actions
    };

  } catch (error) {
    console.error('修复会话失败:', error);
    return {
      success: false,
      message: `修复失败: ${error instanceof Error ? error.message : '未知错误'}`,
      actions
    };
  }
}

/**
 * 获取系统级别的诊断信息
 */
export async function getSystemDiagnostics() {
  try {
    const client = await getMongoClient();
    const db = client.db();
    
    // 获取各集合的统计信息
    const [sessionsStats, checkpointsStats, checkpointWritesStats] = await Promise.all([
      db.collection(SESSIONS).aggregate([
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            active: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
            archived: { $sum: { $cond: [{ $eq: ['$status', 'archived'] }, 1, 0] } }
          }
        }
      ]).toArray(),
      db.collection(CHECKPOINT_COLLECTION_NAME).countDocuments(),
      db.collection(CHECKPOINT_WRITES_COLLECTION_NAME).countDocuments()
    ]);

    return {
      mongodb_health: true,
      collections: {
        sessions: sessionsStats[0] || { total: 0, active: 0, archived: 0 },
        checkpoints: checkpointsStats,
        checkpoint_writes: checkpointWritesStats
      },
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('获取系统诊断信息失败:', error);
    return {
      mongodb_health: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    };
  }
}
