import { getMongoClient, getMongoCheckpointer } from '@/config/mongodb';
import { SessionInfo, updateSessionInfo } from './session-manager';
import { SESSIONS, SESSION_BACKUPS } from '../config/constants';

// 生命周期配置接口
interface LifecycleConfig {
  autoArchiveAfterDays: number;
  autoDeleteAfterDays: number;
  backupRetentionDays: number;
  maxSessionsPerUser: number;
}

// 备份数据接口
interface SessionBackup {
  session_info: SessionInfo;
  checkpoints: any[];
  created_at: Date;
  backup_type: 'manual' | 'auto';
}

// 默认生命周期配置
const DEFAULT_LIFECYCLE_CONFIG: LifecycleConfig = {
  autoArchiveAfterDays: 30,
  autoDeleteAfterDays: 90,
  backupRetentionDays: 365,
  maxSessionsPerUser: 100
};

/**
 * 自动归档过期会话
 */
export async function autoArchiveSessions(config: Partial<LifecycleConfig> = {}): Promise<number> {
  const finalConfig = { ...DEFAULT_LIFECYCLE_CONFIG, ...config };
  
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection(SESSIONS);
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - finalConfig.autoArchiveAfterDays);
    
    console.log(`SessionLifecycle: 开始归档 ${finalConfig.autoArchiveAfterDays} 天前的会话`);
    
    const result = await collection.updateMany(
      {
        last_accessed: { $lt: cutoffDate },
        status: 'active'
      },
      {
        $set: {
          status: 'archived',
          updated_at: new Date(),
          archived_at: new Date(),
          archived_reason: 'auto_archive_inactive'
        }
      }
    );
    
    console.log(`SessionLifecycle: 已归档 ${result.modifiedCount} 个会话`);
    return result.modifiedCount;
  } catch (error) {
    console.error('SessionLifecycle: 自动归档失败', error);
    return 0;
  }
}

/**
 * 自动删除过期会话
 */
export async function autoDeleteSessions(config: Partial<LifecycleConfig> = {}): Promise<number> {
  const finalConfig = { ...DEFAULT_LIFECYCLE_CONFIG, ...config };
  
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection(SESSIONS);
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - finalConfig.autoDeleteAfterDays);
    
    console.log(`SessionLifecycle: 开始删除 ${finalConfig.autoDeleteAfterDays} 天前的会话`);
    
    // 先备份要删除的会话
    const sessionsToDelete = await collection.find({
      $or: [
        { last_accessed: { $lt: cutoffDate }, status: 'archived' },
        { created_at: { $lt: cutoffDate }, status: 'deleted' }
      ]
    }).toArray();
    
    // 创建备份
    for (const session of sessionsToDelete) {
      await createSessionBackup(session.thread_id, 'auto');
    }
    
    // 标记为已删除而不是物理删除
    const result = await collection.updateMany(
      {
        $or: [
          { last_accessed: { $lt: cutoffDate }, status: 'archived' },
          { created_at: { $lt: cutoffDate }, status: 'deleted' }
        ]
      },
      {
        $set: {
          status: 'deleted',
          updated_at: new Date(),
          deleted_at: new Date(),
          deleted_reason: 'auto_delete_expired'
        }
      }
    );
    
    console.log(`SessionLifecycle: 已删除 ${result.modifiedCount} 个会话`);
    return result.modifiedCount;
  } catch (error) {
    console.error('SessionLifecycle: 自动删除失败', error);
    return 0;
  }
}

/**
 * 创建会话备份
 */
export async function createSessionBackup(
  threadId: string, 
  backupType: 'manual' | 'auto' = 'manual'
): Promise<boolean> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const sessionsCollection = db.collection(SESSIONS);
    const backupsCollection = db.collection(SESSION_BACKUPS);
    
    // 获取会话信息
    const session = await sessionsCollection.findOne({ thread_id: threadId });
    if (!session) {
      console.error(`SessionLifecycle: 会话 ${threadId} 不存在，无法备份`);
      return false;
    }
    
    // 获取检查点数据
    const checkpointer = await getMongoCheckpointer();
    const config = { configurable: { thread_id: threadId } };
    const checkpoints = [];
    
    try {
      for await (const checkpoint of checkpointer.list(config)) {
        checkpoints.push({
          checkpoint_id: checkpoint.config.configurable?.checkpoint_id,
          timestamp: checkpoint.checkpoint?.ts,
          step: checkpoint.metadata?.step,
          source: checkpoint.metadata?.source,
          data: checkpoint.checkpoint
        });
      }
    } catch (checkpointError) {
      console.warn(`SessionLifecycle: 获取检查点失败 ${threadId}`, checkpointError);
    }
    
    // 创建备份记录
    const backup: SessionBackup = {
      session_info: session as unknown as SessionInfo,
      checkpoints,
      created_at: new Date(),
      backup_type: backupType
    };
    
    await backupsCollection.insertOne({
      thread_id: threadId,
      ...backup
    });
    
    console.log(`SessionLifecycle: 会话 ${threadId} 备份完成 (${backupType})`);
    return true;
  } catch (error) {
    console.error(`SessionLifecycle: 备份会话 ${threadId} 失败`, error);
    return false;
  }
}

/**
 * 恢复会话从备份
 */
export async function restoreSessionFromBackup(threadId: string): Promise<boolean> {
  try {
    const client = await getMongoClient();
    const db = client.db();
    const sessionsCollection = db.collection(SESSIONS);
    const backupsCollection = db.collection(SESSION_BACKUPS);
    
    // 查找最新的备份
    const backup = await backupsCollection.findOne(
      { thread_id: threadId },
      { sort: { created_at: -1 } }
    );
    
    if (!backup) {
      console.error(`SessionLifecycle: 没有找到会话 ${threadId} 的备份`);
      return false;
    }
    
    // 恢复会话信息
    const restoredSession = {
      ...backup.session_info,
      status: 'active',
      updated_at: new Date(),
      restored_at: new Date(),
      restored_from_backup: backup.created_at
    };
    
    await sessionsCollection.replaceOne(
      { thread_id: threadId },
      restoredSession,
      { upsert: true }
    );
    
    // 恢复检查点数据（如果有的话）
    if (backup.checkpoints && backup.checkpoints.length > 0) {
      try {
        const checkpointer = await getMongoCheckpointer();
        // 注意：这里需要根据具体的checkpointer实现来恢复数据
        // 由于MongoDBSaver的内部实现，直接恢复检查点可能比较复杂
        console.log(`SessionLifecycle: 会话 ${threadId} 有 ${backup.checkpoints.length} 个检查点需要恢复`);
      } catch (checkpointError) {
        console.warn(`SessionLifecycle: 恢复检查点失败 ${threadId}`, checkpointError);
      }
    }
    
    console.log(`SessionLifecycle: 会话 ${threadId} 恢复完成`);
    return true;
  } catch (error) {
    console.error(`SessionLifecycle: 恢复会话 ${threadId} 失败`, error);
    return false;
  }
}

/**
 * 清理过期备份
 */
export async function cleanupExpiredBackups(config: Partial<LifecycleConfig> = {}): Promise<number> {
  const finalConfig = { ...DEFAULT_LIFECYCLE_CONFIG, ...config };
  
  try {
    const client = await getMongoClient();
    const db = client.db();
    const backupsCollection = db.collection(SESSION_BACKUPS);
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - finalConfig.backupRetentionDays);
    
    console.log(`SessionLifecycle: 开始清理 ${finalConfig.backupRetentionDays} 天前的备份`);
    
    const result = await backupsCollection.deleteMany({
      created_at: { $lt: cutoffDate }
    });
    
    console.log(`SessionLifecycle: 已清理 ${result.deletedCount} 个过期备份`);
    return result.deletedCount;
  } catch (error) {
    console.error('SessionLifecycle: 清理过期备份失败', error);
    return 0;
  }
}

/**
 * 限制用户会话数量
 */
export async function enforceUserSessionLimit(
  userId: string, 
  config: Partial<LifecycleConfig> = {}
): Promise<number> {
  const finalConfig = { ...DEFAULT_LIFECYCLE_CONFIG, ...config };
  
  try {
    const client = await getMongoClient();
    const db = client.db();
    const collection = db.collection(SESSIONS);
    
    // 获取用户的活跃会话，按最后访问时间排序
    const userSessions = await collection.find({
      user_id: userId,
      status: 'active'
    }).sort({ last_accessed: -1 }).toArray();
    
    if (userSessions.length <= finalConfig.maxSessionsPerUser) {
      return 0; // 没有超出限制
    }
    
    // 归档超出限制的旧会话
    const sessionsToArchive = userSessions.slice(finalConfig.maxSessionsPerUser);
    const threadIdsToArchive = sessionsToArchive.map(s => s.thread_id);
    
    const result = await collection.updateMany(
      { thread_id: { $in: threadIdsToArchive } },
      {
        $set: {
          status: 'archived',
          updated_at: new Date(),
          archived_at: new Date(),
          archived_reason: 'user_session_limit'
        }
      }
    );
    
    console.log(`SessionLifecycle: 用户 ${userId} 超出会话限制，已归档 ${result.modifiedCount} 个会话`);
    return result.modifiedCount;
  } catch (error) {
    console.error(`SessionLifecycle: 限制用户 ${userId} 会话数量失败`, error);
    return 0;
  }
}

/**
 * 执行完整的生命周期维护
 */
export async function runLifecycleMaintenance(config: Partial<LifecycleConfig> = {}): Promise<{
  archivedCount: number;
  deletedCount: number;
  cleanedBackupsCount: number;
}> {
  console.log('SessionLifecycle: 开始执行生命周期维护');
  
  const archivedCount = await autoArchiveSessions(config);
  const deletedCount = await autoDeleteSessions(config);
  const cleanedBackupsCount = await cleanupExpiredBackups(config);
  
  console.log('SessionLifecycle: 生命周期维护完成', {
    archivedCount,
    deletedCount,
    cleanedBackupsCount
  });
  
  return {
    archivedCount,
    deletedCount,
    cleanedBackupsCount
  };
}
