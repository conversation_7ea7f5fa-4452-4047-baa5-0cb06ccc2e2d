/**
 * 消息类型验证和标准化工具
 */

// 标准消息类型定义
export type StandardMessageType = 'human' | 'ai' | 'system' | 'tool' | 'unknown';
export type StandardMessageRole = 'user' | 'assistant' | 'system' | 'unknown';

// 消息接口定义
export interface StandardMessage {
  id: string | number;
  type: StandardMessageType;
  role: StandardMessageRole;
  content: string;
  timestamp: string;
  metadata?: {
    originalType?: string;
    originalRole?: string;
    source?: 'langchain' | 'vercel' | 'custom';
  };
}

/**
 * LangChain 消息类型到标准类型的映射
 */
const LANGCHAIN_TYPE_MAP: Record<string, StandardMessageType> = {
  'human': 'human',
  'ai': 'ai',
  'system': 'system',
  'tool': 'tool',
  'chat': 'unknown', // ChatMessage 通常需要特殊处理
};

/**
 * 标准类型到角色的映射
 */
const TYPE_TO_ROLE_MAP: Record<StandardMessageType, StandardMessageRole> = {
  'human': 'user',
  'ai': 'assistant',
  'system': 'system',
  'tool': 'system', // 工具消息在UI中显示为系统消息
  'unknown': 'unknown',
};

/**
 * 验证并标准化消息类型
 */
export function validateAndNormalizeMessageType(
  rawType: string | undefined,
  fallbackRole?: string
): { type: StandardMessageType; role: StandardMessageRole } {
  
  // 首先尝试从 LangChain 类型映射
  if (rawType && LANGCHAIN_TYPE_MAP[rawType]) {
    const type = LANGCHAIN_TYPE_MAP[rawType];
    return {
      type,
      role: TYPE_TO_ROLE_MAP[type]
    };
  }

  // 如果没有类型信息，尝试从角色推断
  if (fallbackRole) {
    switch (fallbackRole.toLowerCase()) {
      case 'user':
        return { type: 'human', role: 'user' };
      case 'assistant':
        return { type: 'ai', role: 'assistant' };
      case 'system':
        return { type: 'system', role: 'system' };
      default:
        console.warn(`MessageValidator: 未知角色类型 "${fallbackRole}"`);
        return { type: 'unknown', role: 'unknown' };
    }
  }

  // 最后的兜底
  console.warn(`MessageValidator: 无法确定消息类型`, { rawType, fallbackRole });
  return { type: 'unknown', role: 'unknown' };
}

/**
 * 标准化消息对象
 */
export function normalizeMessage(
  rawMessage: any,
  index: number,
  defaultTimestamp: string
): StandardMessage {
  const originalType = rawMessage._type || rawMessage.type;
  const originalRole = rawMessage.role;
  
  const { type, role } = validateAndNormalizeMessageType(originalType, originalRole);
  
  return {
    id: rawMessage.id || index,
    type,
    role,
    content: rawMessage.content || '',
    timestamp: rawMessage.timestamp || defaultTimestamp,
    metadata: {
      originalType,
      originalRole,
      source: 'langchain'
    }
  };
}

/**
 * 批量标准化消息数组
 */
export function normalizeMessages(
  rawMessages: any[],
  defaultTimestamp: string
): StandardMessage[] {
  if (!Array.isArray(rawMessages)) {
    console.warn('MessageValidator: 输入不是数组', typeof rawMessages);
    return [];
  }

  return rawMessages.map((msg, index) => normalizeMessage(msg, index, defaultTimestamp));
}

/**
 * 验证消息数组的完整性
 */
export function validateMessageArray(messages: StandardMessage[]): {
  isValid: boolean;
  issues: string[];
  stats: {
    total: number;
    byType: Record<StandardMessageType, number>;
    byRole: Record<StandardMessageRole, number>;
    unknownCount: number;
  };
} {
  const issues: string[] = [];
  const stats = {
    total: messages.length,
    byType: {} as Record<StandardMessageType, number>,
    byRole: {} as Record<StandardMessageRole, number>,
    unknownCount: 0
  };

  // 初始化统计
  (['human', 'ai', 'system', 'tool', 'unknown'] as StandardMessageType[]).forEach(type => {
    stats.byType[type] = 0;
  });
  (['user', 'assistant', 'system', 'unknown'] as StandardMessageRole[]).forEach(role => {
    stats.byRole[role] = 0;
  });

  messages.forEach((msg, index) => {
    // 统计
    stats.byType[msg.type]++;
    stats.byRole[msg.role]++;
    
    if (msg.type === 'unknown' || msg.role === 'unknown') {
      stats.unknownCount++;
      issues.push(`消息 ${index}: 类型或角色未知 (type: ${msg.type}, role: ${msg.role})`);
    }

    // 验证内容
    if (!msg.content && msg.content !== '') {
      issues.push(`消息 ${index}: 内容为空或未定义`);
    }

    // 验证时间戳
    if (!msg.timestamp) {
      issues.push(`消息 ${index}: 缺少时间戳`);
    }
  });

  return {
    isValid: issues.length === 0,
    issues,
    stats
  };
}
