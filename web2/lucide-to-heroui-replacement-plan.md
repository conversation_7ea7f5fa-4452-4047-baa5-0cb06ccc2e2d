# @radix-ui 和 Lucide-React 到 @heroui/react 替换计划

## 项目概述
本文档记录了将 web2 项目中的 @radix-ui 组件和 lucide-react 图标组件替换为 @heroui/react 组件的详细计划和进度。

## 当前状态
- @heroui/react 已安装 (版本 2.7.5)
- 项目中同时使用了 @radix-ui 和 lucide-react
- 需要将两者都替换为 @heroui/react 的等效组件

## 分析结果

### 使用 @radix-ui 的组件列表
项目中使用了以下 @radix-ui 组件：
- @radix-ui/react-checkbox (在 src/components/ui/checkbox.tsx)
- @radix-ui/react-dialog (在 src/components/ui/dialog.tsx)
- @radix-ui/react-dropdown-menu (在 src/components/ui/dropdown-menu.tsx)
- @radix-ui/react-select (在 src/components/ui/select.tsx)
- @radix-ui/react-popover
- @radix-ui/react-slot
- @radix-ui/react-tabs

### 使用 lucide-react 的文件列表
通过代码扫描，发现以下 14 个文件使用了 lucide-react：

1. `src/app/sessions/page.tsx`
2. `src/components/ui/dialog.tsx`
3. `src/components/ui/checkbox.tsx`
4. `src/components/ui/dropdown-menu.tsx`
5. `src/components/ui/select.tsx`
6. `src/components/Navbar.tsx`
7. `src/components/ChatWindow.tsx`
8. `src/components/ModelSelector.tsx`
9. `src/components/CodingChatWindow.tsx`
10. `src/components/ErrorRecovery.tsx`
11. `src/components/ChatMessageBubble.tsx`
12. `src/components/SessionStateViewer.tsx`
13. `src/components/ErrorMessageBubble.tsx`
14. `src/components/ChatInput.tsx`

### 使用的 lucide-react 图标统计
根据导入语句分析，项目中使用了以下图标：

#### src/app/sessions/page.tsx
- Search, Filter, Eye, Trash2, Archive, Star, MessageSquare, Code, Clock, BarChart3

#### src/components/ui/
- dialog.tsx: X
- checkbox.tsx: Check
- dropdown-menu.tsx: Check, ChevronRight, Circle
- select.tsx: Check, ChevronDown, ChevronUp

#### src/components/
- Navbar.tsx: Sparkles, MessageCircle, Code2, Settings
- ChatWindow.tsx: ArrowDown, AlertCircle, RefreshCw
- ModelSelector.tsx: Settings, ChevronDown
- CodingChatWindow.tsx: ArrowDown, ArrowUpIcon, LoaderCircle, X, Plus, FileText
- ErrorRecovery.tsx: AlertCircle, RefreshCw, Settings, Zap, Clock
- ChatMessageBubble.tsx: Bot, User, Loader2
- SessionStateViewer.tsx: AlertCircle, CheckCircle, RefreshCw, MessageSquare, Database, Clock
- ErrorMessageBubble.tsx: AlertCircle, RefreshCw, Zap, Clock, Wifi, AlertTriangle
- ChatInput.tsx: ArrowUpIcon, LoaderCircle

## 替换策略

### 1. @heroui/react 组件能力分析
@heroui/react 主要提供UI组件，包括：
- Button, Card, Input, Modal, Navbar, Table 等UI组件
- Spinner (可替换 Loader2, LoaderCircle)
- 但不包含图标组件

### 2. 图标替换方案
由于 @heroui/react 不提供图标组件，我们需要采用以下策略：

#### 方案A：使用 Heroicons (推荐)
- 安装 @heroicons/react
- 提供与 lucide-react 类似的图标集
- 有 outline 和 solid 两种风格

#### 方案B：保留部分 lucide-react
- 仅替换可以用 @heroui/react 组件替代的部分
- 保留纯图标使用 lucide-react

#### 方案C：使用 React Icons
- 安装 react-icons
- 包含多个图标库，包括 Lucide

### 3. 推荐替换映射

#### @radix-ui 到 @heroui/react 组件映射
```
@radix-ui/react-checkbox -> @heroui/react Checkbox
@radix-ui/react-dialog -> @heroui/react Modal
@radix-ui/react-dropdown-menu -> @heroui/react Dropdown
@radix-ui/react-select -> @heroui/react Select
@radix-ui/react-popover -> @heroui/react Popover
@radix-ui/react-tabs -> @heroui/react Tabs
```

#### UI组件替换 (使用 @heroui/react)
- Spinner 组件可替换: Loader2, LoaderCircle
- Button 组件已在使用
- Input, Modal, Navbar, Table 等组件已在使用

#### 图标替换 (建议使用 @heroicons/react)
```
lucide-react -> @heroicons/react/24/outline
- Search -> MagnifyingGlassIcon
- Filter -> FunnelIcon  
- Eye -> EyeIcon
- Trash2 -> TrashIcon
- Archive -> ArchiveBoxIcon
- Star -> StarIcon
- MessageSquare -> ChatBubbleLeftIcon
- Code -> CodeBracketIcon
- Clock -> ClockIcon
- BarChart3 -> ChartBarIcon
- X -> XMarkIcon
- Check -> CheckIcon
- ChevronDown -> ChevronDownIcon
- ChevronUp -> ChevronUpIcon
- ChevronRight -> ChevronRightIcon
- ArrowDown -> ArrowDownIcon
- ArrowUpIcon -> ArrowUpIcon
- AlertCircle -> ExclamationCircleIcon
- RefreshCw -> ArrowPathIcon
- Settings -> CogIcon
- Sparkles -> SparklesIcon
- MessageCircle -> ChatBubbleOvalLeftIcon
- Code2 -> CodeBracketSquareIcon
- Plus -> PlusIcon
- FileText -> DocumentTextIcon
- Zap -> BoltIcon
- Bot -> ComputerDesktopIcon
- User -> UserIcon
- CheckCircle -> CheckCircleIcon
- Database -> CircleStackIcon
- Wifi -> WifiIcon
- AlertTriangle -> ExclamationTriangleIcon
- Circle -> CircleIcon (或使用 CSS)
```

## 实施计划

### 阶段1：准备工作
- [x] 分析现有代码，统计使用情况
- [x] 安装 @heroicons/react
- [ ] 创建图标映射文档

### 阶段2：UI组件文件替换
- [ ] src/components/ui/dialog.tsx
- [ ] src/components/ui/checkbox.tsx  
- [ ] src/components/ui/dropdown-menu.tsx
- [ ] src/components/ui/select.tsx

### 阶段3：页面和组件文件替换
- [ ] src/components/Navbar.tsx
- [ ] src/components/ChatWindow.tsx
- [ ] src/components/ModelSelector.tsx
- [ ] src/components/CodingChatWindow.tsx
- [ ] src/components/ErrorRecovery.tsx
- [ ] src/components/ChatMessageBubble.tsx
- [ ] src/components/SessionStateViewer.tsx
- [ ] src/components/ErrorMessageBubble.tsx
- [ ] src/components/ChatInput.tsx
- [ ] src/app/sessions/page.tsx

### 阶段4：测试和优化
- [ ] 功能测试
- [ ] UI一致性检查
- [ ] 性能测试
- [ ] 清理未使用的依赖

## 进度跟踪

### 已完成
- [x] 代码分析和文件统计
- [x] 替换计划制定
- [x] UI组件文件替换完成：
  - [x] src/components/ui/checkbox.tsx - 替换为 @heroui/react Checkbox
  - [x] src/components/ui/select.tsx - 替换为 @heroui/react Select
  - [x] src/components/ui/dialog.tsx - 替换为 @heroui/react Modal
  - [x] src/components/ui/dropdown-menu.tsx - 替换为 @heroui/react Dropdown

### 进行中
- [/] 页面组件文件替换
  - [x] src/components/Navbar.tsx - 已替换 Sparkles, MessageCircle, Code2, Settings
  - [x] src/components/ChatWindow.tsx - 已替换 ArrowDown, AlertCircle, RefreshCw
  - [x] src/components/ModelSelector.tsx - 已替换 Settings, ChevronDown
  - [x] src/components/CodingChatWindow.tsx - 已替换 ArrowDown, ArrowUpIcon, LoaderCircle, X, Plus, FileText
  - [x] src/components/ErrorRecovery.tsx - 已替换 AlertCircle, RefreshCw, Settings, Zap, Clock
  - [ ] src/components/ChatMessageBubble.tsx
  - [ ] src/components/SessionStateViewer.tsx
  - [ ] src/components/ErrorMessageBubble.tsx
  - [ ] src/components/ChatInput.tsx
  - [ ] src/app/sessions/page.tsx

### 待开始
- [ ] 测试验证
- [ ] 清理未使用的依赖

## 注意事项

1. **图标尺寸**: @heroicons/react 默认24x24，lucide-react 默认24x24，尺寸兼容
2. **样式类名**: 需要检查和调整相关的CSS类名
3. **导入路径**: @heroicons/react 使用不同的导入路径结构
4. **TypeScript**: 确保类型定义正确
5. **测试**: 每个文件替换后都需要测试功能是否正常

## 回滚计划
如果替换过程中出现问题，可以：
1. 使用 git 回滚到替换前的状态
2. 逐个文件回滚有问题的替换
3. 保留原有 lucide-react 依赖作为备选方案
